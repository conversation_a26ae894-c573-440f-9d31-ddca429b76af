import { PrismaClient, Role, LeaveType, LeaveStatus, RecurrenceType } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedScheduleData() {
  try {
    console.log('🌱 Seeding schedule management data...');

    // Tìm clinic và doctor đã có
    const clinic = await prisma.clinic.findFirst();
    if (!clinic) {
      throw new Error('Không tìm thấy clinic. Vui lòng chạy seed chính trước.');
    }

    const doctor = await prisma.doctor.findFirst({
      include: { user: true },
    });
    if (!doctor) {
      throw new Error('Không tìm thấy doctor. Vui lòng chạy seed chính trước.');
    }

    // Tạo clinic admin nếu chưa có
    let clinicAdmin = await prisma.clinicAdmin.findFirst();
    if (!clinicAdmin) {
      const password = await bcrypt.hash('123456789', 10);
      
      const adminUser = await prisma.user.create({
        data: {
          name: '<PERSON><PERSON>',
          dateOfBirth: new Date('1980-01-01'),
          gender: 'male',
          address: 'TP HCM',
          citizenId: '9999999999',
          role: Role.CLINIC_ADMIN,
          auth: {
            create: {
              phone: '0999999999',
              email: '<EMAIL>',
              password,
            },
          },
        },
      });

      clinicAdmin = await prisma.clinicAdmin.create({
        data: {
          clinicAdminCode: 'ADMIN001',
          userId: adminUser.id,
          clinicId: clinic.id,
        },
      });

      console.log('✅ Created clinic admin');
    }

    // 1. Tạo lịch làm việc cơ bản (template hàng tuần)
    const schedules = [
      { dayOfWeek: 1, startTime: '08:00', endTime: '17:00' }, // Thứ 2
      { dayOfWeek: 2, startTime: '08:00', endTime: '17:00' }, // Thứ 3
      { dayOfWeek: 3, startTime: '08:00', endTime: '17:00' }, // Thứ 4
      { dayOfWeek: 4, startTime: '08:00', endTime: '17:00' }, // Thứ 5
      { dayOfWeek: 5, startTime: '08:00', endTime: '12:00' }, // Thứ 6 (chỉ buổi sáng)
    ];

    for (const scheduleData of schedules) {
      await prisma.schedule.upsert({
        where: {
          doctorId_dayOfWeek: {
            doctorId: doctor.id,
            dayOfWeek: scheduleData.dayOfWeek,
          },
        },
        update: scheduleData,
        create: {
          doctorId: doctor.id,
          ...scheduleData,
        },
      });
    }
    console.log('✅ Created basic schedules');

    // 2. Tạo lịch làm việc chi tiết cho tháng 1/2024
    const year = 2024;
    const month = 1;
    const daysInMonth = new Date(year, month, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay();

      // Tìm lịch làm việc cho ngày này
      const schedule = schedules.find(s => s.dayOfWeek === dayOfWeek);

      if (schedule) {
        await prisma.workingHours.upsert({
          where: {
            doctorId_date: {
              doctorId: doctor.id,
              date: date,
            },
          },
          update: {
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            isAvailable: true,
          },
          create: {
            doctorId: doctor.id,
            date: date,
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            isAvailable: true,
          },
        });
      }
    }
    console.log('✅ Created working hours for January 2024');

    // 3. Tạo một số yêu cầu nghỉ phép mẫu
    const leaveRequests = [
      {
        leaveType: LeaveType.PERSONAL_LEAVE,
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-01-15'),
        reason: 'Nghỉ phép cá nhân',
        status: LeaveStatus.APPROVED,
      },
      {
        leaveType: LeaveType.SICK_LEAVE,
        startDate: new Date('2024-01-22'),
        endDate: new Date('2024-01-23'),
        reason: 'Nghỉ ốm',
        status: LeaveStatus.PENDING,
      },
      {
        leaveType: LeaveType.STUDY_LEAVE,
        startDate: new Date('2024-01-08'),
        endDate: new Date('2024-01-08'),
        startTime: '08:00',
        endTime: '12:00',
        isFullDay: false,
        reason: 'Tham gia khóa học',
        status: LeaveStatus.APPROVED,
        recurrenceType: RecurrenceType.WEEKLY,
        recurrenceEnd: new Date('2024-01-29'),
      },
    ];

    for (const leaveData of leaveRequests) {
      const leave = await prisma.doctorLeave.create({
        data: {
          doctorId: doctor.id,
          ...leaveData,
          approvedBy: leaveData.status === LeaveStatus.APPROVED ? clinicAdmin.userId : null,
          approvedAt: leaveData.status === LeaveStatus.APPROVED ? new Date() : null,
        },
      });

      console.log(`✅ Created leave request: ${leave.id}`);
    }

    console.log('🎉 Schedule management data seeded successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Doctor: ${doctor.user.name} (${doctor.id})`);
    console.log(`- Clinic Admin: Admin Phòng Khám (${clinicAdmin.userId})`);
    console.log('- Basic schedules: Monday-Friday');
    console.log('- Working hours: January 2024');
    console.log('- Leave requests: 3 samples');
    console.log('\n🔑 Login credentials:');
    console.log('- Admin: <EMAIL> / 123456789');
    console.log('- Doctor: <EMAIL> / 123456789');

  } catch (error) {
    console.error('❌ Error seeding schedule data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedScheduleData()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedScheduleData };
