{"info": {"name": "Schedule Management API Tests", "description": "Test scripts cho module qu<PERSON><PERSON> lý lịch rảnh bác sĩ", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}, {"key": "doctor<PERSON><PERSON>", "value": "", "type": "string"}, {"key": "doctorId", "value": "", "type": "string"}, {"key": "scheduleId", "value": "", "type": "string"}, {"key": "leaveId", "value": "", "type": "string"}], "item": [{"name": "1. Authentication", "item": [{"name": "<PERSON><PERSON> as Clinic Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"password\": \"123456789\"\n}"}, "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('adminToken', response.access_token);", "    console.log('Admin token saved:', response.access_token);", "}"]}}]}, {"name": "<PERSON><PERSON> as Doctor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"password\": \"123456789\"\n}"}, "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('doctorToken', response.access_token);", "    console.log('Doctor token saved:', response.access_token);", "}"]}}]}]}, {"name": "2. Schedule Management", "item": [{"name": "Create Basic Schedule - Monday", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"doctorId\": \"{{doctorId}}\",\n  \"dayOfWeek\": 1,\n  \"startTime\": \"08:00\",\n  \"endTime\": \"17:00\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/schedule-management/schedules", "host": ["{{baseUrl}}"], "path": ["schedule-management", "schedules"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('scheduleId', response.id);", "    console.log('Schedule created:', response.id);", "}"]}}]}, {"name": "Get Doctor Schedules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/schedule-management/schedules/doctor/{{doctorId}}", "host": ["{{baseUrl}}"], "path": ["schedule-management", "schedules", "doctor", "{{doctorId}}"]}}}, {"name": "Get Clinic Schedules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/schedule-management/schedules/clinic", "host": ["{{baseUrl}}"], "path": ["schedule-management", "schedules", "clinic"]}}}]}, {"name": "3. Working Hours Management", "item": [{"name": "Bulk Create Working Hours for January 2024", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"doctorId\": \"{{doctorId}}\",\n  \"month\": 1,\n  \"year\": 2024,\n  \"overwrite\": false\n}"}, "url": {"raw": "{{baseUrl}}/schedule-management/working-hours/bulk", "host": ["{{baseUrl}}"], "path": ["schedule-management", "working-hours", "bulk"]}}}, {"name": "Get Doctor Working Hours", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/schedule-management/working-hours/doctor/{{doctorId}}?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["schedule-management", "working-hours", "doctor", "{{doctorId}}"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}, {"name": "Get Available Time Slots", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/schedule-management/working-hours/available-slots?doctorId={{doctorId}}&startDate=2024-01-15&endDate=2024-01-31&duration=30", "host": ["{{baseUrl}}"], "path": ["schedule-management", "working-hours", "available-slots"], "query": [{"key": "doctorId", "value": "{{doctorId}}"}, {"key": "startDate", "value": "2024-01-15"}, {"key": "endDate", "value": "2024-01-31"}, {"key": "duration", "value": "30"}]}}}]}, {"name": "4. Doctor Leave Management", "item": [{"name": "Doctor Create Leave Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{doctor<PERSON><PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"doctorId\": \"{{doctorId}}\",\n  \"leaveType\": \"PERSONAL_LEAVE\",\n  \"startDate\": \"2024-01-20\",\n  \"endDate\": \"2024-01-21\",\n  \"isFullDay\": true,\n  \"reason\": \"Nghỉ phép cá nhân\",\n  \"recurrenceType\": \"NONE\"\n}"}, "url": {"raw": "{{baseUrl}}/schedule-management/leaves", "host": ["{{baseUrl}}"], "path": ["schedule-management", "leaves"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('leaveId', response.id);", "    console.log('Leave request created:', response.id);", "}"]}}]}, {"name": "Admin Approve Leave Request", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"APPROVED\",\n  \"notes\": \"Đã duyệt yêu cầu nghỉ phép\"\n}"}, "url": {"raw": "{{baseUrl}}/schedule-management/leaves/{{leaveId}}/status", "host": ["{{baseUrl}}"], "path": ["schedule-management", "leaves", "{{leaveId}}", "status"]}}}, {"name": "Get Leave Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/schedule-management/leaves", "host": ["{{baseUrl}}"], "path": ["schedule-management", "leaves"]}}}, {"name": "Get Approved Leaves", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/schedule-management/leaves/doctor/{{doctorId}}/approved?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["schedule-management", "leaves", "doctor", "{{doctorId}}", "approved"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}}]}]}