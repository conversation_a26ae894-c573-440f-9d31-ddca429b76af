import { IsNotEmpty, IsString, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsBoolean, IsOptional, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateScheduleDto {
  @ApiProperty({ description: 'ID của bác sĩ' })
  @IsNotEmpty()
  @IsString()
  doctorId: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> trong tuần (0=Chủ nhật, 1=Thứ 2, ..., 6=Thứ 7)', minimum: 0, maximum: 6 })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  @Max(6)
  dayOfWeek: number;

  @ApiProperty({ description: 'Thời gian bắt đầu (HH:mm)', example: '08:00' })
  @IsNotEmpty()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Thời gian phải có định dạng HH:mm' })
  startTime: string;

  @ApiProperty({ description: 'Thời gian kết thúc (HH:mm)', example: '17:00' })
  @IsNotEmpty()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Thời gian phải có định dạng HH:mm' })
  endTime: string;

  @ApiProperty({ description: 'Có đang hoạt động không', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateScheduleDto {
  @ApiProperty({ description: 'Thời gian bắt đầu (HH:mm)', example: '08:00', required: false })
  @IsOptional()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Thời gian phải có định dạng HH:mm' })
  startTime?: string;

  @ApiProperty({ description: 'Thời gian kết thúc (HH:mm)', example: '17:00', required: false })
  @IsOptional()
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Thời gian phải có định dạng HH:mm' })
  endTime?: string;

  @ApiProperty({ description: 'Có đang hoạt động không', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
