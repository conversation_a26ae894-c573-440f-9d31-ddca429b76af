import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ScheduleService } from './schedule.service';
import { CreateScheduleDto, UpdateScheduleDto } from './dto';
import { JwtAuthGuard } from '../login/jwt-auth.guard';
import { RolesGuard } from '../rbac/roles.guard';
import { Roles } from '../rbac/roles.decorator';
import { Role } from '../rbac/roles.enum';

@ApiTags('Schedule Management - Lịch làm việc cơ bản')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('schedule-management/schedules')
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post()
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Tạo lịch làm việc cơ bản cho bác sĩ' })
  @ApiResponse({ status: 201, description: 'Tạo lịch thành công' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async createSchedule(@Body() createScheduleDto: CreateScheduleDto, @Request() req) {
    return this.scheduleService.createSchedule(createScheduleDto, req.user.id);
  }

  @Put(':id')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Cập nhật lịch làm việc cơ bản' })
  @ApiResponse({ status: 200, description: 'Cập nhật thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy lịch làm việc' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async updateSchedule(
    @Param('id') id: string,
    @Body() updateScheduleDto: UpdateScheduleDto,
    @Request() req,
  ) {
    return this.scheduleService.updateSchedule(id, updateScheduleDto, req.user.id);
  }

  @Get('doctor/:doctorId')
  @ApiOperation({ summary: 'Lấy danh sách lịch làm việc cơ bản của bác sĩ' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy bác sĩ' })
  async getDoctorSchedules(@Param('doctorId') doctorId: string) {
    return this.scheduleService.getDoctorSchedules(doctorId);
  }

  @Get('clinic')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Lấy danh sách lịch làm việc của tất cả bác sĩ trong phòng khám' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách thành công' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async getClinicSchedules(@Request() req) {
    return this.scheduleService.getClinicSchedules(req.user.id);
  }

  @Delete(':id')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Xóa lịch làm việc cơ bản' })
  @ApiResponse({ status: 200, description: 'Xóa thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy lịch làm việc' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async deleteSchedule(@Param('id') id: string, @Request() req) {
    return this.scheduleService.deleteSchedule(id, req.user.id);
  }
}
