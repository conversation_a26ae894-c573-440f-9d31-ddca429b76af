# Schedule Management Module

Module quản lý lịch rảnh của bác sĩ trong hệ thống đặt lịch khám bệnh.

## Tính năng chính

### 1. Qu<PERSON>n lý lịch làm việ<PERSON> c<PERSON> bản (Schedule)
- Tạo template lịch làm việc hàng tuần cho bác sĩ
- <PERSON><PERSON><PERSON> nh<PERSON>, x<PERSON><PERSON> lịch làm việc cơ bản
- <PERSON>em lịch làm việc của bác sĩ hoặc toàn phòng khám

### 2. Quản lý lịch nghỉ (Doctor Leave)
- Tạ<PERSON> yêu cầu nghỉ phép (định kỳ hoặc đột xuất)
- Duyệt/từ chối yêu cầu nghỉ phép
- Hỗ trợ các loại nghỉ: ốm, cá nhân, học tập, khẩn cấp, v.v.
- Quy định nghỉ trước 3 ngày (trừ khẩn cấp)
- Hỗ trợ lịch nghỉ lặp lại

### 3. <PERSON><PERSON><PERSON><PERSON> lý lịch làm việc chi tiết (Working Hours)
- <PERSON><PERSON><PERSON> lịch làm việc cụ thể cho từng ngày
- Tạo hàng loạt lịch làm việc cho cả tháng
- Tìm khung giờ trống của bác sĩ

## Quyền truy cập

- **CLINIC_ADMIN**: Toàn quyền quản lý lịch của tất cả bác sĩ trong phòng khám
- **DOCTOR**: Chỉ có thể tạo yêu cầu nghỉ phép cho chính mình
- **Khách**: Có thể xem lịch rảnh của bác sĩ

## API Endpoints

### Schedule Management
```
POST   /schedule-management/schedules              # Tạo lịch cơ bản
PUT    /schedule-management/schedules/:id          # Cập nhật lịch cơ bản
GET    /schedule-management/schedules/doctor/:id   # Xem lịch bác sĩ
GET    /schedule-management/schedules/clinic       # Xem lịch phòng khám
DELETE /schedule-management/schedules/:id          # Xóa lịch cơ bản
```

### Doctor Leave Management
```
POST   /schedule-management/leaves                 # Tạo yêu cầu nghỉ
PUT    /schedule-management/leaves/:id/status      # Duyệt yêu cầu nghỉ
GET    /schedule-management/leaves                 # Xem danh sách yêu cầu
GET    /schedule-management/leaves/doctor/:id/approved # Xem lịch nghỉ đã duyệt
```

### Working Hours Management
```
POST   /schedule-management/working-hours          # Tạo lịch chi tiết
PUT    /schedule-management/working-hours/:id      # Cập nhật lịch chi tiết
POST   /schedule-management/working-hours/bulk     # Tạo hàng loạt
GET    /schedule-management/working-hours/doctor/:id # Xem lịch chi tiết
GET    /schedule-management/working-hours/available-slots # Tìm khung giờ trống
```

## Workflow sử dụng

### 1. Thiết lập lịch làm việc cơ bản
```bash
# Admin tạo lịch làm việc hàng tuần cho bác sĩ
POST /schedule-management/schedules
{
  "doctorId": "doctor-uuid",
  "dayOfWeek": 1,  # Thứ 2
  "startTime": "08:00",
  "endTime": "17:00"
}
```

### 2. Tạo lịch làm việc chi tiết cho tháng
```bash
# Tạo hàng loạt dựa trên template
POST /schedule-management/working-hours/bulk
{
  "doctorId": "doctor-uuid",
  "month": 1,
  "year": 2024,
  "overwrite": false
}
```

### 3. Bác sĩ đăng ký nghỉ phép
```bash
# Bác sĩ tạo yêu cầu nghỉ
POST /schedule-management/leaves
{
  "doctorId": "doctor-uuid",
  "leaveType": "PERSONAL_LEAVE",
  "startDate": "2024-01-15",
  "endDate": "2024-01-16",
  "reason": "Nghỉ phép cá nhân"
}
```

### 4. Admin duyệt yêu cầu nghỉ
```bash
# Admin duyệt yêu cầu
PUT /schedule-management/leaves/:id/status
{
  "status": "APPROVED",
  "notes": "Đã duyệt"
}
```

### 5. Tìm khung giờ trống
```bash
# Tìm khung giờ trống của bác sĩ
GET /schedule-management/working-hours/available-slots?doctorId=uuid&startDate=2024-01-15&endDate=2024-01-31&duration=30
```

## Database Schema

### Schedule (Lịch cơ bản)
- Template lịch làm việc hàng tuần
- Mỗi bác sĩ có thể có nhiều lịch cho các ngày khác nhau

### DoctorLeave (Lịch nghỉ)
- Yêu cầu nghỉ phép với các trạng thái: PENDING, APPROVED, REJECTED, CANCELLED
- Hỗ trợ nghỉ cả ngày hoặc một phần ngày
- Hỗ trợ lịch nghỉ lặp lại

### WorkingHours (Lịch chi tiết)
- Lịch làm việc cụ thể cho từng ngày
- Được tạo dựa trên template Schedule

## Quy tắc nghiệp vụ

1. **Quy định nghỉ phép**: Phải đăng ký trước 3 ngày (trừ khẩn cấp)
2. **Không xung đột**: Không được tạo lịch nghỉ trùng nhau
3. **Quyền quản lý**: Chỉ admin phòng khám mới có quyền duyệt và quản lý lịch
4. **Tự động tạo lịch**: Có thể tạo hàng loạt lịch làm việc dựa trên template
5. **Tìm khung giờ trống**: Tự động loại trừ lịch nghỉ và cuộc hẹn đã đặt
