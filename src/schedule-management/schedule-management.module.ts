import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { ScheduleController } from './schedule.controller';
import { Doctor<PERSON>eaveController } from './doctor-leave.controller';
import { WorkingHoursController } from './working-hours.controller';
import { ScheduleService } from './schedule.service';
import { DoctorLeaveService } from './doctor-leave.service';
import { WorkingHoursService } from './working-hours.service';

@Module({
  imports: [PrismaModule],
  controllers: [
    ScheduleController,
    DoctorLeaveController,
    WorkingHoursController,
  ],
  providers: [
    ScheduleService,
    DoctorLeaveService,
    WorkingHoursService,
  ],
  exports: [
    ScheduleService,
    DoctorLeaveService,
    WorkingHoursService,
  ],
})
export class ScheduleManagementModule {}
