import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateScheduleDto, UpdateScheduleDto } from './dto';
import { Role } from '@prisma/client';

@Injectable()
export class ScheduleService {
  constructor(private prisma: PrismaService) {}

  /**
   * Tạo lịch làm việc c<PERSON> bản cho bác sĩ (template hàng tuần)
   */
  async createSchedule(createScheduleDto: CreateScheduleDto, adminUserId: string) {
    // <PERSON><PERSON>m tra quyền admin
    await this.validateClinicAdmin(adminUserId, createScheduleDto.doctorId);

    // Kiểm tra bác sĩ tồn tại
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: createScheduleDto.doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('<PERSON>h<PERSON><PERSON> tìm thấy bác sĩ');
    }

    // <PERSON><PERSON>m tra thời gian hợp lệ
    this.validateTimeRange(createScheduleDto.startTime, createScheduleDto.endTime);

    // Kiểm tra lịch đã tồn tại
    const existingSchedule = await this.prisma.schedule.findUnique({
      where: {
        doctorId_dayOfWeek: {
          doctorId: createScheduleDto.doctorId,
          dayOfWeek: createScheduleDto.dayOfWeek,
        },
      },
    });

    if (existingSchedule) {
      throw new BadRequestException('Lịch làm việc cho ngày này đã tồn tại');
    }

    return this.prisma.schedule.create({
      data: {
        ...createScheduleDto,
        isActive: createScheduleDto.isActive ?? true,
      },
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Cập nhật lịch làm việc cơ bản
   */
  async updateSchedule(scheduleId: string, updateScheduleDto: UpdateScheduleDto, adminUserId: string) {
    const schedule = await this.prisma.schedule.findUnique({
      where: { id: scheduleId },
      include: { doctor: true },
    });

    if (!schedule) {
      throw new NotFoundException('Không tìm thấy lịch làm việc');
    }

    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, schedule.doctorId);

    // Kiểm tra thời gian hợp lệ nếu có cập nhật
    if (updateScheduleDto.startTime && updateScheduleDto.endTime) {
      this.validateTimeRange(updateScheduleDto.startTime, updateScheduleDto.endTime);
    }

    return this.prisma.schedule.update({
      where: { id: scheduleId },
      data: updateScheduleDto,
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Lấy danh sách lịch làm việc cơ bản của bác sĩ
   */
  async getDoctorSchedules(doctorId: string) {
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('Không tìm thấy bác sĩ');
    }

    return this.prisma.schedule.findMany({
      where: { doctorId },
      orderBy: { dayOfWeek: 'asc' },
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Lấy danh sách lịch làm việc của tất cả bác sĩ trong phòng khám
   */
  async getClinicSchedules(adminUserId: string) {
    const admin = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      include: { clinicAdmin: true },
    });

    if (!admin?.clinicAdmin) {
      throw new ForbiddenException('Chỉ admin phòng khám mới có quyền truy cập');
    }

    return this.prisma.schedule.findMany({
      where: {
        doctor: {
          clinicId: admin.clinicAdmin.clinicId,
        },
      },
      orderBy: [
        { doctor: { user: { name: 'asc' } } },
        { dayOfWeek: 'asc' },
      ],
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Xóa lịch làm việc
   */
  async deleteSchedule(scheduleId: string, adminUserId: string) {
    const schedule = await this.prisma.schedule.findUnique({
      where: { id: scheduleId },
    });

    if (!schedule) {
      throw new NotFoundException('Không tìm thấy lịch làm việc');
    }

    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, schedule.doctorId);

    return this.prisma.schedule.delete({
      where: { id: scheduleId },
    });
  }

  /**
   * Kiểm tra quyền admin phòng khám
   */
  private async validateClinicAdmin(adminUserId: string, doctorId: string) {
    const admin = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      include: { clinicAdmin: true },
    });

    if (!admin || admin.role !== Role.CLINIC_ADMIN || !admin.clinicAdmin) {
      throw new ForbiddenException('Chỉ admin phòng khám mới có quyền thực hiện thao tác này');
    }

    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor || doctor.clinicId !== admin.clinicAdmin.clinicId) {
      throw new ForbiddenException('Bạn chỉ có thể quản lý bác sĩ trong phòng khám của mình');
    }
  }

  /**
   * Kiểm tra thời gian hợp lệ
   */
  private validateTimeRange(startTime: string, endTime: string) {
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);

    if (start >= end) {
      throw new BadRequestException('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');
    }
  }
}
