import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateWorkingHoursDto, UpdateWorkingHoursDto, BulkCreateWorkingHoursDto, GetAvailableTimeSlotsQueryDto } from './dto';
import { Role } from '@prisma/client';
import { DoctorLeaveService } from './doctor-leave.service';

@Injectable()
export class WorkingHoursService {
  constructor(
    private prisma: PrismaService,
    private doctorLeaveService: DoctorLeaveService,
  ) {}

  /**
   * Tạo lịch làm việc chi tiết cho một ngày
   */
  async createWorkingHours(createWorkingHoursDto: CreateWorkingHoursDto, adminUserId: string) {
    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, createWorkingHoursDto.doctorId);

    // <PERSON><PERSON>m tra bác sĩ tồn tại
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: createWorkingHoursDto.doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('Không tìm thấy bác sĩ');
    }

    // Kiểm tra thời gian hợp lệ
    this.validateTimeRange(createWorkingHoursDto.startTime, createWorkingHoursDto.endTime);

    // Kiểm tra ngày không được trong quá khứ
    const workDate = new Date(createWorkingHoursDto.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (workDate < today) {
      throw new BadRequestException('Không thể tạo lịch làm việc cho ngày trong quá khứ');
    }

    const workingHoursData = {
      ...createWorkingHoursDto,
      date: workDate,
      isAvailable: createWorkingHoursDto.isAvailable ?? true,
    };

    return this.prisma.workingHours.upsert({
      where: {
        doctorId_date: {
          doctorId: createWorkingHoursDto.doctorId,
          date: workDate,
        },
      },
      update: workingHoursData,
      create: workingHoursData,
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Cập nhật lịch làm việc chi tiết
   */
  async updateWorkingHours(workingHoursId: string, updateWorkingHoursDto: UpdateWorkingHoursDto, adminUserId: string) {
    const workingHours = await this.prisma.workingHours.findUnique({
      where: { id: workingHoursId },
    });

    if (!workingHours) {
      throw new NotFoundException('Không tìm thấy lịch làm việc');
    }

    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, workingHours.doctorId);

    // Kiểm tra thời gian hợp lệ nếu có cập nhật
    if (updateWorkingHoursDto.startTime && updateWorkingHoursDto.endTime) {
      this.validateTimeRange(updateWorkingHoursDto.startTime, updateWorkingHoursDto.endTime);
    }

    return this.prisma.workingHours.update({
      where: { id: workingHoursId },
      data: updateWorkingHoursDto,
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Tạo lịch làm việc hàng loạt cho cả tháng dựa trên template
   */
  async bulkCreateWorkingHours(bulkCreateDto: BulkCreateWorkingHoursDto, adminUserId: string) {
    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, bulkCreateDto.doctorId);

    // Lấy template lịch làm việc của bác sĩ
    const schedules = await this.prisma.schedule.findMany({
      where: {
        doctorId: bulkCreateDto.doctorId,
        isActive: true,
      },
    });

    if (schedules.length === 0) {
      throw new BadRequestException('Bác sĩ chưa có lịch làm việc cơ bản. Vui lòng tạo lịch cơ bản trước.');
    }

    // Tạo danh sách ngày trong tháng
    const year = bulkCreateDto.year;
    const month = bulkCreateDto.month;
    const daysInMonth = new Date(year, month, 0).getDate();
    const workingHoursData: any[] = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ...

      // Tìm lịch làm việc cho ngày này
      const schedule = schedules.find(s => s.dayOfWeek === dayOfWeek);

      if (schedule) {
        // Kiểm tra xem đã có lịch làm việc cho ngày này chưa
        if (!bulkCreateDto.overwrite) {
          const existing = await this.prisma.workingHours.findUnique({
            where: {
              doctorId_date: {
                doctorId: bulkCreateDto.doctorId,
                date: date,
              },
            },
          });

          if (existing) {
            continue; // Bỏ qua nếu đã tồn tại và không ghi đè
          }
        }

        workingHoursData.push({
          doctorId: bulkCreateDto.doctorId,
          date: date,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          isAvailable: true,
        });
      }
    }

    // Tạo hàng loạt
    const results: any[] = [];
    for (const data of workingHoursData) {
      const result = await this.prisma.workingHours.upsert({
        where: {
          doctorId_date: {
            doctorId: data.doctorId,
            date: data.date,
          },
        },
        update: data,
        create: data,
      });
      results.push(result);
    }

    return {
      message: `Đã tạo ${results.length} lịch làm việc cho tháng ${month}/${year}`,
      count: results.length,
      data: results,
    };
  }

  /**
   * Lấy lịch làm việc của bác sĩ trong khoảng thời gian
   */
  async getDoctorWorkingHours(doctorId: string, startDate: Date, endDate: Date) {
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('Không tìm thấy bác sĩ');
    }

    return this.prisma.workingHours.findMany({
      where: {
        doctorId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { date: 'asc' },
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Lấy các khung giờ trống của bác sĩ
   */
  async getAvailableTimeSlots(query: GetAvailableTimeSlotsQueryDto) {
    const { doctorId, startDate, endDate, duration = 30 } = query;

    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('Không tìm thấy bác sĩ');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Lấy lịch làm việc
    const workingHours = await this.getDoctorWorkingHours(doctorId, start, end);

    // Lấy lịch nghỉ đã được duyệt
    const approvedLeaves = await this.doctorLeaveService.getApprovedLeaves(doctorId, start, end);

    // Lấy các cuộc hẹn đã đặt
    const appointments = await this.prisma.appointment.findMany({
      where: {
        doctorId,
        date: {
          gte: start,
          lte: end,
        },
        status: { not: 'CANCELLED' },
      },
      select: {
        date: true,
        startTime: true,
        endTime: true,
      },
    });

    // Tính toán khung giờ trống
    const availableSlots: any[] = [];

    for (const workHour of workingHours) {
      if (!workHour.isAvailable) continue;

      // Kiểm tra có nghỉ phép không
      const hasLeave = this.checkLeaveConflict(workHour.date, approvedLeaves);
      if (hasLeave) continue;

      // Tạo các khung giờ
      const slots = this.generateTimeSlots(
        workHour.date,
        workHour.startTime,
        workHour.endTime,
        duration,
        appointments,
      );

      availableSlots.push({
        date: workHour.date,
        slots,
      });
    }

    return availableSlots;
  }

  /**
   * Kiểm tra quyền admin phòng khám
   */
  private async validateClinicAdmin(adminUserId: string, doctorId: string) {
    const admin = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      include: { clinicAdmin: true },
    });

    if (!admin || admin.role !== Role.CLINIC_ADMIN || !admin.clinicAdmin) {
      throw new ForbiddenException('Chỉ admin phòng khám mới có quyền thực hiện thao tác này');
    }

    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor || doctor.clinicId !== admin.clinicAdmin.clinicId) {
      throw new ForbiddenException('Bạn chỉ có thể quản lý bác sĩ trong phòng khám của mình');
    }
  }

  /**
   * Kiểm tra thời gian hợp lệ
   */
  private validateTimeRange(startTime: string, endTime: string) {
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);

    if (start >= end) {
      throw new BadRequestException('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');
    }
  }

  /**
   * Kiểm tra xung đột với lịch nghỉ
   */
  private checkLeaveConflict(date: Date, leaves: any[]): boolean {
    return leaves.some(leave => {
      const leaveStart = new Date(leave.startDate);
      const leaveEnd = new Date(leave.endDate);
      
      // Kiểm tra ngày nằm trong khoảng nghỉ
      if (date >= leaveStart && date <= leaveEnd) {
        return true;
      }

      // TODO: Xử lý lịch nghỉ lặp lại
      return false;
    });
  }

  /**
   * Tạo các khung giờ trống
   */
  private generateTimeSlots(date: Date, startTime: string, endTime: string, duration: number, appointments: any[]) {
    const slots: any[] = [];
    const start = new Date(`${date.toISOString().split('T')[0]}T${startTime}:00`);
    const end = new Date(`${date.toISOString().split('T')[0]}T${endTime}:00`);

    let current = new Date(start);
    
    while (current < end) {
      const slotEnd = new Date(current.getTime() + duration * 60000);
      
      if (slotEnd > end) break;

      // Kiểm tra xung đột với cuộc hẹn
      const hasConflict = appointments.some(apt => {
        const aptDate = new Date(apt.date);
        if (aptDate.toDateString() !== date.toDateString()) return false;

        const aptStart = new Date(`${aptDate.toISOString().split('T')[0]}T${apt.startTime}:00`);
        const aptEnd = new Date(`${aptDate.toISOString().split('T')[0]}T${apt.endTime}:00`);

        return (current < aptEnd && slotEnd > aptStart);
      });

      if (!hasConflict) {
        slots.push({
          startTime: current.toTimeString().slice(0, 5),
          endTime: slotEnd.toTimeString().slice(0, 5),
        });
      }

      current = new Date(slotEnd);
    }

    return slots;
  }
}
