import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDoctorLeaveDto, UpdateDoctorLeaveStatusDto, GetDoctorLeavesQueryDto } from './dto';
import { Role, LeaveStatus, RecurrenceType, LeaveType } from '@prisma/client';

@Injectable()
export class DoctorLeaveService {
  constructor(private prisma: PrismaService) {}

  /**
   * Tạo yêu cầu nghỉ phép
   */
  async createLeaveRequest(createLeaveDto: CreateDoctorLeaveDto, requesterId: string) {
    // Kiểm tra bác sĩ tồn tại và quyền tạo yêu cầu
    const doctor = await this.validateDoctorAccess(createLeaveDto.doctorId, requesterId);

    // <PERSON><PERSON><PERSON> tra thời gian nghỉ hợp lệ
    this.validateLeaveDates(createLeaveDto);

    // Kiểm tra quy định nghỉ trước 3 ngày (trừ nghỉ khẩn cấp)
    if (createLeaveDto.leaveType !== 'EMERGENCY_LEAVE') {
      this.validateAdvanceNotice(createLeaveDto.startDate);
    }

    // Kiểm tra xung đột lịch nghỉ
    await this.checkLeaveConflicts(createLeaveDto);

    const leaveData = {
      ...createLeaveDto,
      startDate: new Date(createLeaveDto.startDate),
      endDate: new Date(createLeaveDto.endDate),
      recurrenceEnd: createLeaveDto.recurrenceEnd ? new Date(createLeaveDto.recurrenceEnd) : null,
      isFullDay: createLeaveDto.isFullDay ?? true,
      recurrenceType: createLeaveDto.recurrenceType ?? RecurrenceType.NONE,
    };

    return this.prisma.doctorLeave.create({
      data: leaveData,
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
      },
    });
  }

  /**
   * Cập nhật trạng thái yêu cầu nghỉ phép (chỉ admin)
   */
  async updateLeaveStatus(leaveId: string, updateStatusDto: UpdateDoctorLeaveStatusDto, adminUserId: string) {
    const leave = await this.prisma.doctorLeave.findUnique({
      where: { id: leaveId },
      include: { doctor: true },
    });

    if (!leave) {
      throw new NotFoundException('Không tìm thấy yêu cầu nghỉ phép');
    }

    // Kiểm tra quyền admin
    await this.validateClinicAdmin(adminUserId, leave.doctorId);

    // Không thể cập nhật yêu cầu đã được xử lý (trừ hủy)
    if (leave.status !== LeaveStatus.PENDING && updateStatusDto.status !== 'CANCELLED') {
      throw new BadRequestException('Chỉ có thể cập nhật yêu cầu đang chờ duyệt');
    }

    const updateData: any = {
      status: updateStatusDto.status,
    };

    if (updateStatusDto.status === 'APPROVED') {
      updateData.approvedAt = new Date();
      updateData.approvedBy = adminUserId;
    }

    if (updateStatusDto.notes) {
      updateData.notes = updateStatusDto.notes;
    }

    return this.prisma.doctorLeave.update({
      where: { id: leaveId },
      data: updateData,
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
        approver: {
          select: { id: true, name: true },
        },
      },
    });
  }

  /**
   * Lấy danh sách yêu cầu nghỉ phép
   */
  async getLeaveRequests(query: GetDoctorLeavesQueryDto, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { clinicAdmin: true, doctor: true },
    });

    if (!user) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    let whereClause: any = {};

    // Nếu là CLINIC_ADMIN, có thể xem tất cả yêu cầu trong phòng khám
    if (user.role === Role.CLINIC_ADMIN && user.clinicAdmin) {
      whereClause.doctor = {
        clinicId: user.clinicAdmin.clinicId,
      };
    }
    // Nếu là DOCTOR, chỉ xem yêu cầu của mình
    else if (user.role === Role.DOCTOR && user.doctor) {
      whereClause.doctorId = user.doctor.id;
    }
    else {
      throw new ForbiddenException('Không có quyền truy cập');
    }

    // Áp dụng các filter
    if (query.doctorId) {
      whereClause.doctorId = query.doctorId;
    }

    if (query.startDate || query.endDate) {
      whereClause.AND = [];
      if (query.startDate) {
        whereClause.AND.push({
          endDate: { gte: new Date(query.startDate) },
        });
      }
      if (query.endDate) {
        whereClause.AND.push({
          startDate: { lte: new Date(query.endDate) },
        });
      }
    }

    if (query.status) {
      whereClause.status = query.status;
    }

    return this.prisma.doctorLeave.findMany({
      where: whereClause,
      orderBy: { requestedAt: 'desc' },
      include: {
        doctor: {
          include: {
            user: {
              select: { id: true, name: true },
            },
          },
        },
        approver: {
          select: { id: true, name: true },
        },
      },
    });
  }

  /**
   * Lấy lịch nghỉ đã được duyệt của bác sĩ trong khoảng thời gian
   */
  async getApprovedLeaves(doctorId: string, startDate: Date, endDate: Date) {
    return this.prisma.doctorLeave.findMany({
      where: {
        doctorId,
        status: LeaveStatus.APPROVED,
        OR: [
          {
            AND: [
              { startDate: { lte: endDate } },
              { endDate: { gte: startDate } },
            ],
          },
          // Xử lý lịch nghỉ lặp lại
          {
            AND: [
              { recurrenceType: { not: RecurrenceType.NONE } },
              { startDate: { lte: endDate } },
              {
                OR: [
                  { recurrenceEnd: null },
                  { recurrenceEnd: { gte: startDate } },
                ],
              },
            ],
          },
        ],
      },
      orderBy: { startDate: 'asc' },
    });
  }

  /**
   * Kiểm tra quyền truy cập bác sĩ
   */
  private async validateDoctorAccess(doctorId: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { doctor: true, clinicAdmin: true },
    });

    if (!user) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor) {
      throw new NotFoundException('Không tìm thấy bác sĩ');
    }

    // Bác sĩ chỉ có thể tạo yêu cầu cho chính mình
    if (user.role === Role.DOCTOR) {
      if (!user.doctor || user.doctor.id !== doctorId) {
        throw new ForbiddenException('Bạn chỉ có thể tạo yêu cầu nghỉ phép cho chính mình');
      }
    }
    // Admin có thể tạo yêu cầu cho bác sĩ trong phòng khám
    else if (user.role === Role.CLINIC_ADMIN) {
      if (!user.clinicAdmin || doctor.clinicId !== user.clinicAdmin.clinicId) {
        throw new ForbiddenException('Bạn chỉ có thể quản lý bác sĩ trong phòng khám của mình');
      }
    }
    else {
      throw new ForbiddenException('Không có quyền thực hiện thao tác này');
    }

    return doctor;
  }

  /**
   * Kiểm tra quyền admin phòng khám
   */
  private async validateClinicAdmin(adminUserId: string, doctorId: string) {
    const admin = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      include: { clinicAdmin: true },
    });

    if (!admin || admin.role !== Role.CLINIC_ADMIN || !admin.clinicAdmin) {
      throw new ForbiddenException('Chỉ admin phòng khám mới có quyền duyệt yêu cầu nghỉ phép');
    }

    const doctor = await this.prisma.doctor.findUnique({
      where: { id: doctorId },
    });

    if (!doctor || doctor.clinicId !== admin.clinicAdmin.clinicId) {
      throw new ForbiddenException('Bạn chỉ có thể duyệt yêu cầu của bác sĩ trong phòng khám của mình');
    }
  }

  /**
   * Kiểm tra thời gian nghỉ hợp lệ
   */
  private validateLeaveDates(createLeaveDto: CreateDoctorLeaveDto) {
    const startDate = new Date(createLeaveDto.startDate);
    const endDate = new Date(createLeaveDto.endDate);

    if (startDate > endDate) {
      throw new BadRequestException('Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc');
    }

    if (!createLeaveDto.isFullDay) {
      if (!createLeaveDto.startTime || !createLeaveDto.endTime) {
        throw new BadRequestException('Phải cung cấp thời gian bắt đầu và kết thúc khi không nghỉ cả ngày');
      }

      const startTime = new Date(`2000-01-01T${createLeaveDto.startTime}:00`);
      const endTime = new Date(`2000-01-01T${createLeaveDto.endTime}:00`);

      if (startTime >= endTime) {
        throw new BadRequestException('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');
      }
    }

    if (createLeaveDto.recurrenceType !== RecurrenceType.NONE && createLeaveDto.recurrenceEnd) {
      const recurrenceEnd = new Date(createLeaveDto.recurrenceEnd);
      if (recurrenceEnd <= endDate) {
        throw new BadRequestException('Ngày kết thúc lặp lại phải sau ngày kết thúc nghỉ');
      }
    }
  }

  /**
   * Kiểm tra quy định nghỉ trước 3 ngày
   */
  private validateAdvanceNotice(startDate: string) {
    const start = new Date(startDate);
    const now = new Date();
    const diffDays = Math.ceil((start.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays < 3) {
      throw new BadRequestException('Phải đăng ký nghỉ phép trước ít nhất 3 ngày (trừ trường hợp khẩn cấp)');
    }
  }

  /**
   * Kiểm tra xung đột lịch nghỉ
   */
  private async checkLeaveConflicts(createLeaveDto: CreateDoctorLeaveDto) {
    const startDate = new Date(createLeaveDto.startDate);
    const endDate = new Date(createLeaveDto.endDate);

    const conflictingLeaves = await this.prisma.doctorLeave.findMany({
      where: {
        doctorId: createLeaveDto.doctorId,
        status: { in: [LeaveStatus.PENDING, LeaveStatus.APPROVED] },
        OR: [
          {
            AND: [
              { startDate: { lte: endDate } },
              { endDate: { gte: startDate } },
            ],
          },
        ],
      },
    });

    if (conflictingLeaves.length > 0) {
      throw new BadRequestException('Đã có lịch nghỉ trong khoảng thời gian này');
    }
  }
}
