import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DoctorLeaveService } from './doctor-leave.service';
import { CreateDoctorLeaveDto, UpdateDoctorLeaveStatusDto, GetDoctorLeavesQueryDto } from './dto';
import { JwtAuthGuard } from '../login/jwt-auth.guard';
import { RolesGuard } from '../rbac/roles.guard';
import { Roles } from '../rbac/roles.decorator';
import { Role } from '../rbac/roles.enum';

@ApiTags('Schedule Management - Quản lý lịch nghỉ')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('schedule-management/leaves')
export class DoctorLeaveController {
  constructor(private readonly doctorLeaveService: DoctorLeaveService) {}

  @Post()
  @Roles(Role.DOCTOR, Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Tạo yêu cầu nghỉ phép' })
  @ApiResponse({ status: 201, description: 'Tạo yêu cầu thành công' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc vi phạm quy định' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async createLeaveRequest(@Body() createLeaveDto: CreateDoctorLeaveDto, @Request() req) {
    return this.doctorLeaveService.createLeaveRequest(createLeaveDto, req.user.id);
  }

  @Put(':id/status')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Cập nhật trạng thái yêu cầu nghỉ phép (duyệt/từ chối)' })
  @ApiResponse({ status: 200, description: 'Cập nhật trạng thái thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy yêu cầu nghỉ phép' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async updateLeaveStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateDoctorLeaveStatusDto,
    @Request() req,
  ) {
    return this.doctorLeaveService.updateLeaveStatus(id, updateStatusDto, req.user.id);
  }

  @Get()
  @Roles(Role.DOCTOR, Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Lấy danh sách yêu cầu nghỉ phép' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách thành công' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async getLeaveRequests(@Query() query: GetDoctorLeavesQueryDto, @Request() req) {
    return this.doctorLeaveService.getLeaveRequests(query, req.user.id);
  }

  @Get('doctor/:doctorId/approved')
  @ApiOperation({ summary: 'Lấy lịch nghỉ đã được duyệt của bác sĩ' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách thành công' })
  async getApprovedLeaves(
    @Param('doctorId') doctorId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.doctorLeaveService.getApprovedLeaves(
      doctorId,
      new Date(startDate),
      new Date(endDate),
    );
  }
}
