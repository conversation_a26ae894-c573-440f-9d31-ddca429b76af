import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { WorkingHoursService } from './working-hours.service';
import { CreateWorkingHoursDto, UpdateWorkingHoursDto, BulkCreateWorkingHoursDto, GetAvailableTimeSlotsQueryDto } from './dto';
import { JwtAuthGuard } from '../login/jwt-auth.guard';
import { RolesGuard } from '../rbac/roles.guard';
import { Roles } from '../rbac/roles.decorator';
import { Role } from '../rbac/roles.enum';

@ApiTags('Schedule Management - Quản lý lịch làm việc chi tiết')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('schedule-management/working-hours')
export class WorkingHoursController {
  constructor(private readonly workingHoursService: WorkingHoursService) {}

  @Post()
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Tạo lịch làm việc chi tiết cho một ngày' })
  @ApiResponse({ status: 201, description: 'Tạo lịch thành công' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async createWorkingHours(@Body() createWorkingHoursDto: CreateWorkingHoursDto, @Request() req) {
    return this.workingHoursService.createWorkingHours(createWorkingHoursDto, req.user.id);
  }

  @Put(':id')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Cập nhật lịch làm việc chi tiết' })
  @ApiResponse({ status: 200, description: 'Cập nhật thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy lịch làm việc' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async updateWorkingHours(
    @Param('id') id: string,
    @Body() updateWorkingHoursDto: UpdateWorkingHoursDto,
    @Request() req,
  ) {
    return this.workingHoursService.updateWorkingHours(id, updateWorkingHoursDto, req.user.id);
  }

  @Post('bulk')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Tạo lịch làm việc hàng loạt cho cả tháng dựa trên template' })
  @ApiResponse({ status: 201, description: 'Tạo lịch hàng loạt thành công' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc chưa có lịch cơ bản' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập' })
  async bulkCreateWorkingHours(@Body() bulkCreateDto: BulkCreateWorkingHoursDto, @Request() req) {
    return this.workingHoursService.bulkCreateWorkingHours(bulkCreateDto, req.user.id);
  }

  @Get('doctor/:doctorId')
  @ApiOperation({ summary: 'Lấy lịch làm việc của bác sĩ trong khoảng thời gian' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy bác sĩ' })
  async getDoctorWorkingHours(
    @Param('doctorId') doctorId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.workingHoursService.getDoctorWorkingHours(
      doctorId,
      new Date(startDate),
      new Date(endDate),
    );
  }

  @Get('available-slots')
  @ApiOperation({ summary: 'Lấy các khung giờ trống của bác sĩ' })
  @ApiResponse({ status: 200, description: 'Lấy danh sách khung giờ trống thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy bác sĩ' })
  async getAvailableTimeSlots(@Query() query: GetAvailableTimeSlotsQueryDto) {
    return this.workingHoursService.getAvailableTimeSlots(query);
  }
}
